{"name": "linkwork-frontend-web", "version": "2.0.0", "description": "Link-Work 前端Web应用 - 现代化React架构", "private": true, "homepage": "./", "main": "public/index.html", "dependencies": {"@tanstack/react-query": "^5.89.0", "@tanstack/react-query-devtools": "^5.89.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-virtualized": "^9.22.5", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react-beautiful-dnd": "^13.1.8", "@types/react-virtualized": "^9.21.29", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "vite": "^5.0.5", "vitest": "^1.0.4"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}