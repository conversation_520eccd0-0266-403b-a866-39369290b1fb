{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowDownToDot = createLucideIcon(\"ArrowDownToDot\", [[\"path\", {\n  d: \"M12 2v14\",\n  key: \"jyx4ut\"\n}], [\"path\", {\n  d: \"m19 9-7 7-7-7\",\n  key: \"1oe3oy\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"21\",\n  r: \"1\",\n  key: \"o0uj5v\"\n}]]);\nexport { ArrowDownToDot as default };", "map": {"version": 3, "names": ["ArrowDownToDot", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\customerDemo\\Link-Work\\node_modules\\lucide-react\\src\\icons\\arrow-down-to-dot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDownToDot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYxNCIgLz4KICA8cGF0aCBkPSJtMTkgOS03IDctNy03IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMjEiIHI9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-down-to-dot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownToDot = createLucideIcon('ArrowDownToDot', [\n  ['path', { d: 'M12 2v14', key: 'jyx4ut' }],\n  ['path', { d: 'm19 9-7 7-7-7', key: '1oe3oy' }],\n  ['circle', { cx: '12', cy: '21', r: '1', key: 'o0uj5v' }],\n]);\n\nexport default ArrowDownToDot;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}