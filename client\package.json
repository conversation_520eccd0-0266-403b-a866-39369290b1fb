{"name": "niuma-client", "version": "1.0.0", "description": "尊贵的打工人 - 专为中国上班族打造的趣味任务管理工具", "main": "public/electron.js", "homepage": "./", "private": true, "dependencies": {"date-fns": "^2.29.3", "electron-store": "^8.1.0", "framer-motion": "^10.16.4", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "uuid": "^9.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "electron-builder", "preelectron-pack": "npm run build", "dist": "npm run build && electron-builder --publish=never", "dist-all": "npm run build && electron-builder -mwl --publish=never", "dist-win": "npm run build && electron-builder --win --publish=never", "dist-mac": "npm run build && electron-builder --mac --publish=never", "dist-linux": "npm run build && electron-builder --linux --publish=never"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "autoprefixer": "^10.4.14", "concurrently": "^7.6.0", "cross-env": "^10.0.0", "electron": "^25.9.8", "electron-builder": "^24.13.3", "electron-is-dev": "^3.0.1", "jest-environment-jsdom": "^30.1.2", "postcss": "^8.4.24", "tailwindcss": "^3.3.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.niuma.perpetual-motion", "productName": "尊贵的打工人", "directories": {"output": "dist"}, "asar": false, "files": ["build/**/*", "public/electron.js", "public/preload.js", "public/icon.png", "public/tray-icon.png", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "dir"}, "linux": {"target": "AppImage"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "forceCodeSigning": false}}