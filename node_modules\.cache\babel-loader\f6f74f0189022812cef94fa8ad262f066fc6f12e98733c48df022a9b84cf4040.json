{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowDownLeftFromCircle = createLucideIcon(\"ArrowDownLeftFromCircle\", [[\"path\", {\n  d: \"M2 12a10 10 0 1 1 10 10\",\n  key: \"1yn6ov\"\n}], [\"path\", {\n  d: \"m2 22 10-10\",\n  key: \"28ilpk\"\n}], [\"path\", {\n  d: \"M8 22H2v-6\",\n  key: \"sulq54\"\n}]]);\nexport { ArrowDownLeftFromCircle as default };", "map": {"version": 3, "names": ["ArrowDownLeftFromCircle", "createLucideIcon", "d", "key"], "sources": ["D:\\customerDemo\\Link-Work\\node_modules\\lucide-react\\src\\icons\\arrow-down-left-from-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDownLeftFromCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMmExMCAxMCAwIDEgMSAxMCAxMCIgLz4KICA8cGF0aCBkPSJtMiAyMiAxMC0xMCIgLz4KICA8cGF0aCBkPSJNOCAyMkgydi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-down-left-from-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownLeftFromCircle = createLucideIcon('ArrowDownLeftFromCircle', [\n  ['path', { d: 'M2 12a10 10 0 1 1 10 10', key: '1yn6ov' }],\n  ['path', { d: 'm2 22 10-10', key: '28ilpk' }],\n  ['path', { d: 'M8 22H2v-6', key: 'sulq54' }],\n]);\n\nexport default ArrowDownLeftFromCircle;\n"], "mappings": ";;;;;AAaM,MAAAA,uBAAA,GAA0BC,gBAAA,CAAiB,yBAA2B,GAC1E,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}