{"ast": null, "code": "import { useCallback } from 'react';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n  return useCallback(instance => {\n    instance && visualState.mount && visualState.mount(instance);\n    if (visualElement) {\n      instance ? visualElement.mount(instance) : visualElement.unmount();\n    }\n    if (externalRef) {\n      if (typeof externalRef === \"function\") {\n        externalRef(instance);\n      } else if (isRefObject(externalRef)) {\n        externalRef.current = instance;\n      }\n    }\n  },\n  /**\n   * Only pass a new ref callback to React if we've received a visual element\n   * factory. Otherwise we'll be mounting/remounting every time externalRef\n   * or other dependencies change.\n   */\n  [visualElement]);\n}\nexport { useMotionRef };", "map": {"version": 3, "names": ["useCallback", "isRefObject", "useMotionRef", "visualState", "visualElement", "externalRef", "instance", "mount", "unmount", "current"], "sources": ["D:/customerDemo/Link-Work/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs"], "sourcesContent": ["import { useCallback } from 'react';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return useCallback((instance) => {\n        instance && visualState.mount && visualState.mount(instance);\n        if (visualElement) {\n            instance\n                ? visualElement.mount(instance)\n                : visualElement.unmount();\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if (isRefObject(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to <PERSON>act if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\nexport { useMotionRef };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,WAAW,QAAQ,+BAA+B;;AAE3D;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAE;EAC3D,OAAOL,WAAW,CAAEM,QAAQ,IAAK;IAC7BA,QAAQ,IAAIH,WAAW,CAACI,KAAK,IAAIJ,WAAW,CAACI,KAAK,CAACD,QAAQ,CAAC;IAC5D,IAAIF,aAAa,EAAE;MACfE,QAAQ,GACFF,aAAa,CAACG,KAAK,CAACD,QAAQ,CAAC,GAC7BF,aAAa,CAACI,OAAO,CAAC,CAAC;IACjC;IACA,IAAIH,WAAW,EAAE;MACb,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;QACnCA,WAAW,CAACC,QAAQ,CAAC;MACzB,CAAC,MACI,IAAIL,WAAW,CAACI,WAAW,CAAC,EAAE;QAC/BA,WAAW,CAACI,OAAO,GAAGH,QAAQ;MAClC;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,CAACF,aAAa,CAAC,CAAC;AACpB;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}